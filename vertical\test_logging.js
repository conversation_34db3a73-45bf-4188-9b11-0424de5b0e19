// Simple test to verify the logging functions work correctly
// This simulates the logging functionality from main.ts

function formatTimestamp() {
  return new Date().toISOString();
}

function logInfo(requestId, step, message, data) {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.log(`[${timestamp}] [INFO] [${requestId}] [${step}] ${message}${logData}`);
}

function logError(requestId, step, message, error) {
  const timestamp = formatTimestamp();
  const errorData = error ? ` | 错误: ${error instanceof Error ? error.message : JSON.stringify(error)}` : '';
  console.error(`[${timestamp}] [ERROR] [${requestId}] [${step}] ${message}${errorData}`);
}

function logWarning(requestId, step, message, data) {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.warn(`[${timestamp}] [WARN] [${requestId}] [${step}] ${message}${logData}`);
}

// Test the logging functions
console.log("=== 测试日志功能 ===\n");

const testRequestId = "test123";

// Test INFO logging
logInfo(testRequestId, "MAIN", "开始处理聊天完成请求");

logInfo(testRequestId, "MAIN", "解析请求体成功", {
  model: "claude-3-5-sonnet-20241022",
  messagesCount: 2,
  stream: false,
  temperature: 0.7
});

// Test ERROR logging
logError(testRequestId, "AUTH", "认证失败：缺少有效的Bearer token", {
  authHeader: "Bearer invalid_token..."
});

// Test WARNING logging
logWarning(testRequestId, "ADAPTER", "未识别的行格式", {
  line: "unknown_format_line...",
  lineLength: 50
});

// Test complex data logging
logInfo(testRequestId, "MESSAGES", "消息处理完成", {
  messageRoles: ["system", "user"],
  systemPromptLength: 150,
  latestUserMessageLength: 25,
  hasSystemPrompt: true,
  hasUserMessage: true
});

console.log("\n=== 日志测试完成 ===");
