# Vertical Studio AI 接口更新完成 - 最终版本

## 🎉 更新完成！

根据您提供的完整请求头信息，我已经修正了所有遗漏的部分，确保与实际的 Vertical Studio AI 接口完全匹配。

## 📋 最终更新内容

### 1. getChatId 方法 ✅
- **URL 格式**：`GET /stream/models/gpt-4o.data?forceNewChat=true`
- **完整请求头**：包含所有必要的浏览器头部信息
- **正确的 Host 头**：`app.verticalstudio.ai`
- **完整的 Cookie 支持**：使用完整的 cookie 字符串
- **响应处理**：支持重定向和响应体解析

### 2. sendMessageStream 方法 ✅
- **API 端点**：`POST https://app.verticalstudio.ai/api/chat`
- **请求格式**：新的消息对象格式
- **完整请求头**：与浏览器请求完全匹配
- **Cookie 认证**：使用完整的 cookie 字符串

### 3. 认证方式 ✅
- **Cookie 格式**：支持完整的 cookie 字符串，包含：
  - `deviceId=...`
  - `sb-ppdjlmajmpcqpkdmnzfd-auth-token=...`
  - `_clck=...`
  - 等所有必要的 cookies
- **自动检测**：自动识别完整 cookie 字符串
- **向后兼容**：保持对单独 token 的支持

### 4. 请求头完整性 ✅
根据您提供的实际请求头，添加了所有必要的头部：
- `host`
- `sentry-trace`
- `sec-ch-ua-platform`
- `accept-encoding`
- `priority`
- 等等

## 🚀 使用方法

### 1. 获取完整的 Cookie 字符串
从浏览器开发者工具中复制完整的 cookie 字符串：

```
deviceId=IjI1YWFiNmQ0LTRmN2ItNDA3Mi05MGQ1LTNkMGE4NTQ1YmJlMSI%3D.%2FyW1PGLxp8KZuXbBbMTNd%2BX%2BkQupSIv8WBTjthn3Too; initial_referer=eyJ1cmwiOiIkZGlyZWN0IiwiZG9tYWluIjoiJGRpcmVjdCJ9.I84i3pEgdLAC4TQsyKUOW%2F3aQVfQAn2DMnFEzqqzHAo; wagmi.store={"state":{"connections":{"__type":"Map","value":[]},"chainId":1,"current":null},"version":2}; has_seen_survey_popup=true; _clck=1ouwwak%7C2%7Cfwl%7C0%7C1985; _fbp=fb.1.1749384021440.820057507397158309; toast-session=e30%3D.; _hjSessionUser_5346215=eyJpZCI6IjY1NGJhODYyLTkwY2YtNTZhOC1hNjQyLWI2MTlmOWI0N2YwYSIsImNyZWF0ZWQiOjE3NDkzODQwMjUxNjgsImV4aXN0aW5nIjp0cnVlfQ==; sb-ppdjlmajmpcqpkdmnzfd-auth-token=base64-eyJhY2Nlc3NfdG9rZW4iOi...; _clsk=hyb7d5%7C1749397704543%7C2%7C1%7Ck.clarity.ms%2Fcollect
```

### 2. 启动服务器
```bash
deno run --allow-net vertical/main.ts
```

### 3. 发送请求
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

## 🔍 关键修正

### 1. Cookie 处理
- **之前**：只使用 `sb-ppdjlmajmpcqpkdmnzfd-auth-token` 的值
- **现在**：使用完整的 cookie 字符串，包含所有必要的 cookies

### 2. 请求头完整性
- **之前**：缺少一些重要的请求头
- **现在**：包含所有实际浏览器请求中的头部信息

### 3. 认证检测
- **改进**：自动检测完整 cookie 字符串格式
- **兼容**：同时支持单独 token 和完整 cookie

## ✅ 验证清单

- [x] getChatId 使用正确的 URL 和请求头
- [x] sendMessageStream 使用正确的 API 端点
- [x] 支持完整的 cookie 字符串认证
- [x] 包含所有必要的请求头
- [x] 响应解析适配新格式
- [x] 保持 OpenAI 兼容性
- [x] 支持流式和非流式响应

## 🎯 测试建议

1. **基本连接测试**：
   ```bash
   curl http://localhost:8000/health
   ```

2. **模型列表测试**：
   ```bash
   curl -H "Authorization: Bearer test" http://localhost:8000/v1/models
   ```

3. **聊天测试**：使用完整的 cookie 字符串进行实际聊天测试

## 📝 注意事项

1. **Cookie 有效期**：cookies 可能会过期，需要定期更新
2. **完整性**：确保提供完整的 cookie 字符串，不要遗漏任何部分
3. **格式**：cookie 字符串应该是分号分隔的格式
4. **安全性**：妥善保管 cookie 信息，避免泄露

现在的实现应该与实际的 Vertical Studio AI 接口完全匹配！🎉
