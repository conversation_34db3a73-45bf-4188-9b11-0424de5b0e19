# Vertical Studio AI 接口更新测试

## 更新内容总结

### 1. getChatId 方法更新
- ✅ 更新为使用新的 URL 格式：`{modelUrl}?forceNewChat=true`
- ✅ 改为 GET 请求而不是 POST
- ✅ 添加完整的请求头（包括 cookies 和其他必要头部）
- ✅ 支持从重定向响应中提取 chatId
- ✅ 支持从响应体中解析 chatId

### 2. sendMessageStream 方法更新
- ✅ 更新为使用新的端点：`https://app.verticalstudio.ai/api/chat`
- ✅ 更新请求格式，包含 message 对象、chatId、settings 等
- ✅ 添加完整的请求头和 cookies
- ✅ 支持自定义系统提示

### 3. 响应解析更新
- ✅ 适配新的响应格式：数字开头的行（如 `0:"Hello"`）
- ✅ 处理新的结束信号格式（`e:` 和 `d:` 开头）
- ✅ 改进缓冲区处理，避免行分割问题

### 4. 认证方式更新
- ✅ 支持完整的 cookie 认证字符串
- ✅ 自动检测 cookie 格式
- ✅ 向后兼容 Bearer token 格式

### 5. 模型配置更新
- ✅ 添加 GPT-4o 模型支持
- ✅ 保持现有 Claude 模型配置

## 测试建议

### 1. 基本功能测试
```bash
# 启动服务器
deno run --allow-net vertical/main.ts

# 测试模型列表
curl http://localhost:8000/v1/models \
  -H "Authorization: Bearer test-key"

# 测试健康检查
curl http://localhost:8000/health
```

### 2. 聊天完成测试
```bash
# 使用 GPT-4o 模型
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer test-key" \
  -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

### 3. 流式响应测试
```bash
# 测试流式响应
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer test-key" \
  -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": true
  }'
```

## 关键变更点

1. **URL 格式变更**：从 POST 请求变为 GET 请求，添加 `?forceNewChat=true` 参数
2. **认证方式变更**：从 Bearer token 变为完整的 cookie 字符串
3. **API 端点变更**：聊天 API 从模型特定 URL 变为统一的 `/api/chat` 端点
4. **请求格式变更**：消息格式更复杂，包含 id、createdAt、parts 等字段
5. **响应格式变更**：从 `0:"content"` 格式变为数字前缀格式

## 注意事项

1. 需要提供完整的 cookie 字符串，包括 `sb-ppdjlmajmpcqpkdmnzfd-auth-token` 等
2. 新的响应格式需要正确解析数字前缀的内容行
3. 系统提示现在通过 settings 对象传递
4. 需要生成唯一的消息 ID 和时间戳

## 兼容性

- ✅ 保持 OpenAI 兼容的 API 格式
- ✅ 支持流式和非流式响应
- ✅ 向后兼容现有的客户端代码
- ✅ 支持多种模型（GPT-4o, Claude 4 Opus, Claude 4 Sonnet）
