// 简单的语法检查脚本
// 这个脚本用于验证 TypeScript 代码的基本语法正确性

const fs = require('fs');

try {
  const content = fs.readFileSync('main.ts', 'utf8');
  
  // 基本语法检查
  const checks = [
    {
      name: '括号匹配检查',
      test: () => {
        const openBraces = (content.match(/\{/g) || []).length;
        const closeBraces = (content.match(/\}/g) || []).length;
        return openBraces === closeBraces;
      }
    },
    {
      name: '圆括号匹配检查',
      test: () => {
        const openParens = (content.match(/\(/g) || []).length;
        const closeParens = (content.match(/\)/g) || []).length;
        return openParens === closeParens;
      }
    },
    {
      name: '方括号匹配检查',
      test: () => {
        const openBrackets = (content.match(/\[/g) || []).length;
        const closeBrackets = (content.match(/\]/g) || []).length;
        return openBrackets === closeBrackets;
      }
    },
    {
      name: '基本结构检查',
      test: () => {
        return content.includes('class VerticalApiClient') &&
               content.includes('async getChatId') &&
               content.includes('async* sendMessageStream') &&
               content.includes('const MODELS_DATA');
      }
    },
    {
      name: '新接口格式检查',
      test: () => {
        return content.includes('?forceNewChat=true') &&
               content.includes('/api/chat') &&
               content.includes('gpt-4o');
      }
    }
  ];

  console.log('🔍 开始语法检查...\n');
  
  let allPassed = true;
  checks.forEach(check => {
    const result = check.test();
    console.log(`${result ? '✅' : '❌'} ${check.name}: ${result ? '通过' : '失败'}`);
    if (!result) allPassed = false;
  });

  console.log(`\n📊 检查结果: ${allPassed ? '✅ 所有检查通过' : '❌ 存在问题'}`);
  
  if (allPassed) {
    console.log('\n🎉 代码更新成功！主要更新内容：');
    console.log('  • getChatId 方法已更新为新的 API 格式');
    console.log('  • sendMessageStream 方法已适配新的请求格式');
    console.log('  • 响应解析已更新为处理新的数字前缀格式');
    console.log('  • 认证方式已更新为支持完整 cookie 字符串');
    console.log('  • 添加了 GPT-4o 模型支持');
  }

} catch (error) {
  console.error('❌ 读取文件时出错:', error.message);
}
